<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snake Game</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        #game-container {
            position: relative;
        }
        
        #game-canvas {
            border: 2px solid #333;
            background-color: #fff;
        }
        
        #score-display {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 20px;
            color: #333;
        }
        
        #controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Snake Game</h1>
    
    <div id="game-container">
        <canvas id="game-canvas" width="400" height="400">
            Your browser does not support the canvas element.
        </canvas>
        <div id="score-display">Score: 0</div>
        <div id="game-over" class="game-over">
            <h2>Game Over!</h2>
            <p id="final-score">Your score: 0</p>
            <button id="restart-btn">Play Again</button>
        </div>
    </div>
    
    <div id="controls">
        <button id="start-btn">Start Game</button>
        <button id="pause-btn">Pause</button>
    </div>

    <script>
        // Game variables
        const canvas = document.getElementById('game-canvas');
        const ctx = canvas.getContext('2d');
        const scoreDisplay = document.getElementById('score-display');
        const gameOverScreen = document.getElementById('game-over');
        const finalScore = document.getElementById('final-score');
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const restartBtn = document.getElementById('restart-btn');
        
        const gridSize = 20;
        const gridWidth = canvas.width / gridSize;
        const gridHeight = canvas.height / gridSize;
        
        let snake = [];
        let food = {};
        let direction = 'right';
        let nextDirection = 'right';
        let score = 0;
        let gameSpeed = 150; // milliseconds
        let gameLoop;
        let isPaused = false;
        let isGameOver = false;
        let isGameStarted = false;
        
        // Initialize game
        function initGame() {
            // Reset variables
            snake = [
                {x: 5, y: 10},
                {x: 4, y: 10},
                {x: 3, y: 10}
            ];
            direction = 'right';
            nextDirection = 'right';
            score = 0;
            isGameOver = false;
            isPaused = false;
            
            // Update score display
            scoreDisplay.textContent = `Score: ${score}`;
            
            // Hide game over screen
            gameOverScreen.style.display = 'none';
            
            // Generate initial food
            generateFood();
            
            // Draw initial state
            draw();
        }
        
        // Generate food at random position
        function generateFood() {
            // Generate random coordinates
            let foodX, foodY;
            let validPosition = false;
            
            while (!validPosition) {
                foodX = Math.floor(Math.random() * gridWidth);
                foodY = Math.floor(Math.random() * gridHeight);
                
                // Check if food is not on snake
                validPosition = true;
                for (let segment of snake) {
                    if (segment.x === foodX && segment.y === foodY) {
                        validPosition = false;
                        break;
                    }
                }
            }
            
            food = {x: foodX, y: foodY};
        }
        
        // Draw everything
        function draw() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw snake
            for (let i = 0; i < snake.length; i++) {
                const segment = snake[i];
                
                // Head is a different color
                if (i === 0) {
                    ctx.fillStyle = '#2E8B57'; // Dark green for head
                } else {
                    ctx.fillStyle = '#3CB371'; // Medium green for body
                }
                
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize, gridSize);
                
                // Add border to segments
                ctx.strokeStyle = '#fff';
                ctx.strokeRect(segment.x * gridSize, segment.y * gridSize, gridSize, gridSize);
            }
            
            // Draw food
            ctx.fillStyle = '#FF4500'; // Orange-red
            ctx.beginPath();
            const centerX = food.x * gridSize + gridSize / 2;
            const centerY = food.y * gridSize + gridSize / 2;
            const radius = gridSize / 2;
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // Update game state
        function update() {
            // Update direction
            direction = nextDirection;
            
            // Calculate new head position
            const head = {x: snake[0].x, y: snake[0].y};
            
            switch (direction) {
                case 'up':
                    head.y--;
                    break;
                case 'down':
                    head.y++;
                    break;
                case 'left':
                    head.x--;
                    break;
                case 'right':
                    head.x++;
                    break;
            }
            
            // Check for collision with walls
            if (head.x < 0 || head.x >= gridWidth || head.y < 0 || head.y >= gridHeight) {
                gameOver();
                return;
            }
            
            // Check for collision with self
            for (let i = 0; i < snake.length; i++) {
                if (head.x === snake[i].x && head.y === snake[i].y) {
                    gameOver();
                    return;
                }
            }
            
            // Check for food collision
            if (head.x === food.x && head.y === food.y) {
                // Increase score
                score += 10;
                scoreDisplay.textContent = `Score: ${score}`;
                
                // Generate new food
                generateFood();
                
                // Increase speed slightly every 50 points
                if (score % 50 === 0 && gameSpeed > 50) {
                    gameSpeed -= 10;
                    clearInterval(gameLoop);
                    gameLoop = setInterval(update, gameSpeed);
                }
            } else {
                // Remove tail if no food was eaten
                snake.pop();
            }
            
            // Add new head
            snake.unshift(head);
            
            // Draw updated state
            draw();
        }
        
        // Game over function
        function gameOver() {
            isGameOver = true;
            clearInterval(gameLoop);
            
            // Show game over screen
            finalScore.textContent = `Your score: ${score}`;
            gameOverScreen.style.display = 'block';
        }
        
        // Start game function
        function startGame() {
            if (isGameOver || !isGameStarted) {
                initGame();
                isGameStarted = true;
                gameLoop = setInterval(update, gameSpeed);
                startBtn.textContent = 'Restart Game';
            } else if (isPaused) {
                // Resume game
                isPaused = false;
                gameLoop = setInterval(update, gameSpeed);
                pauseBtn.textContent = 'Pause';
            }
        }
        
        // Pause game function
        function pauseGame() {
            if (!isGameOver && isGameStarted) {
                if (isPaused) {
                    // Resume game
                    isPaused = false;
                    gameLoop = setInterval(update, gameSpeed);
                    pauseBtn.textContent = 'Pause';
                } else {
                    // Pause game
                    isPaused = true;
                    clearInterval(gameLoop);
                    pauseBtn.textContent = 'Resume';
                }
            }
        }
        
        // Event listeners
        startBtn.addEventListener('click', startGame);
        pauseBtn.addEventListener('click', pauseGame);
        restartBtn.addEventListener('click', startGame);
        
        // Keyboard controls
        document.addEventListener('keydown', (event) => {
            // Prevent default behavior for arrow keys
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(event.code)) {
                event.preventDefault();
            }
            
            // Update direction based on key press
            // Prevent 180-degree turns
            switch (event.code) {
                case 'ArrowUp':
                    if (direction !== 'down') nextDirection = 'up';
                    break;
                case 'ArrowDown':
                    if (direction !== 'up') nextDirection = 'down';
                    break;
                case 'ArrowLeft':
                    if (direction !== 'right') nextDirection = 'left';
                    break;
                case 'ArrowRight':
                    if (direction !== 'left') nextDirection = 'right';
                    break;
                case 'Space':
                    pauseGame();
                    break;
            }
        });
        
        // Initialize the game board
        initGame();
    </script>
</body>
</html>
